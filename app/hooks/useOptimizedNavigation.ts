import { useNavigate } from "@remix-run/react";
import { useNavigationLoading } from "~/context/navigation-loading-context";
import { startTransition } from "react";

/**
 * Hook for optimized navigation with immediate loading feedback
 */
export function useOptimizedNavigation() {
  const navigate = useNavigate();
  const { showNavigationLoading } = useNavigationLoading();

  /**
   * Navigate with immediate loading feedback
   * @param to The path to navigate to
   * @param options Navigation options
   */
  const navigateWithLoading = (
    to: string,
    options?: Parameters<typeof navigate>[1]
  ) => {
    // Show loading immediately for instant feedback
    showNavigationLoading();
    
    // Use startTransition for non-urgent navigation
    startTransition(() => {
      navigate(to, options);
    });
  };

  /**
   * Navigate with replace and loading feedback
   * @param to The path to navigate to
   */
  const replaceWithLoading = (to: string) => {
    showNavigationLoading();
    
    startTransition(() => {
      navigate(to, { replace: true });
    });
  };

  return { 
    navigateWithLoading, 
    replaceWithLoading,
    // Also expose the original navigate for cases where loading isn't needed
    navigate 
  };
}
