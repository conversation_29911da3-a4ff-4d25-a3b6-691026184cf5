import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "@remix-run/react";
import { useUser } from "~/context/auth-context";
import { createBaseApi } from "~/utils/base-service";
import { formatCurrency } from "~/constants";
import type { GoldPackage, MembershipPackage } from "~/types/index";
import { Flame, CreditCard } from "lucide-react";
import StripePaymentDialog, {
  WrappedElementsPaymentForm,
} from "~/components/payment/StripePaymentDialog";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import LoginDialog from "~/components/auth/LoginDialog";
import RegisterDialog from "~/components/auth/RegisterDialog";

export interface TopUpContentProps {
  title?: string;
  subtitle?: string;
  isInDialog?: boolean;
}

export interface TopUpContentSectionProps {
  title?: string;
  subtitle?: string;
  onPaymentInitiated: (clientSecret: string, amount: number) => void;
  onAuthenticationRequired: () => void;
}

// Reusable TopUp content component
export default function TopUpContent({
  title,
  subtitle,
  isInDialog,
}: TopUpContentProps) {
  const { isLoggedIn } = useUser();
  const navigate = useNavigate();
  const location = useLocation();
  const { isDesktop } = useMediaQuery();

  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [paymentClientSecret, setPaymentClientSecret] = useState<string | null>(
    null
  );
  const [paymentAmount, setPaymentAmount] = useState<number>(0);

  // Login dialog state
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [registerDialogOpen, setRegisterDialogOpen] = useState(false);

  // Check for redirect after login
  useEffect(() => {
    if (isLoggedIn) {
      const redirectUrl = localStorage.getItem("login_redirect_url");
      if (redirectUrl && redirectUrl === location.pathname) {
        localStorage.removeItem("login_redirect_url");
        // User has returned to the same page after login, no need to redirect
      }
    }
  }, [isLoggedIn, location.pathname]);

  // Authentication helper functions

  const handleOpenRegister = () => {
    setLoginDialogOpen(false);
    setRegisterDialogOpen(true);
  };

  const handleOpenLogin = () => {
    setRegisterDialogOpen(false);
    setLoginDialogOpen(true);
  };

  // Mutual exclusion handlers

  const handlePaymentSuccess = () => {
    console.log("Payment successful!");
    // Don't close dialog - let user close it manually from the success screen
    // Optionally refresh user info to show updated coin balance
    // You might want to call a refresh function here
  };

  const handlePaymentError = (error: Error) => {
    console.error("Payment error:", error);
    // Don't close dialog on error - let user see the error and try again
    // The error will be displayed within the dialog
  };

  const handleClosePaymentDialog = () => {
    setShowPaymentDialog(false);
    setPaymentClientSecret(null);
    setPaymentAmount(0);
  };

  const handlePaymentInitiated = (clientSecret: string, amount: number) => {
    setPaymentClientSecret(clientSecret);
    setPaymentAmount(amount);
    setShowPaymentDialog(true);
  };

  const handleAuthenticationRequired = () => {
    // Store current URL for redirect after login
    localStorage.setItem("login_redirect_url", location.pathname);

    if (isDesktop) {
      // Open login dialog on desktop
      setLoginDialogOpen(true);
    } else {
      // Navigate to login page on mobile
      navigate("/login");
    }
  };

  return (
    <>
      {
        // should hide when is inDialog

        (!isInDialog || !showPaymentDialog) && (
          <TopUpContentSection
            title={title}
            subtitle={subtitle}
            onPaymentInitiated={handlePaymentInitiated}
            onAuthenticationRequired={handleAuthenticationRequired}
          />
        )
      }
      {/* Login and Register Dialogs - Only show on desktop */}
      {isDesktop && (
        <>
          <LoginDialog
            open={loginDialogOpen}
            onOpenChange={setLoginDialogOpen}
            onOpenRegister={handleOpenRegister}
          />
          <RegisterDialog
            open={registerDialogOpen}
            onOpenChange={setRegisterDialogOpen}
            onOpenLogin={handleOpenLogin}
          />
        </>
      )}
      {/* Stripe Payment Dialog */}
      {!isInDialog && showPaymentDialog && paymentClientSecret && (
        <StripePaymentDialog
          isOpen={showPaymentDialog}
          onClose={handleClosePaymentDialog}
          clientSecret={paymentClientSecret}
          amount={paymentAmount}
          currency="MYR"
          onPaymentSuccess={handlePaymentSuccess}
          onPaymentError={handlePaymentError}
        />
      )}
      {isInDialog && showPaymentDialog && paymentClientSecret && (
        <WrappedElementsPaymentForm
          clientSecret={paymentClientSecret}
          amount={paymentAmount}
          currency="MYR"
          onPaymentSuccess={handlePaymentSuccess}
          onPaymentError={handlePaymentError}
          onClose={handleClosePaymentDialog}
        />
      )}
    </>
  );
}

export const TopUpContentSection = ({
  title,
  subtitle,
  onPaymentInitiated,
  onAuthenticationRequired,
}: TopUpContentSectionProps) => {
  const { t } = useTranslation();
  const { userInfo, isLoggedIn } = useUser();

  const [goldPackages, setGoldPackages] = useState<GoldPackage[]>([]);
  const [membershipPackages, setMembershipPackages] = useState<
    MembershipPackage[]
  >([]);
  const [payMethods, setPayMethods] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGoldPackage, setSelectedGoldPackage] =
    useState<GoldPackage | null>(null);
  const [selectedMembershipPackage, setSelectedMembershipPackage] =
    useState<MembershipPackage | null>(null);
  const [selectedPayMethod, setSelectedPayMethod] = useState<string | null>(
    null
  );
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);

  useEffect(() => {
    fetchPayConfig();
  }, []);

  const fetchPayConfig = async () => {
    try {
      setIsLoading(true);
      const api = createBaseApi();
      const response = await api.getPayConfig();
      console.log("Pay config response:", response);

      if (response.ok && response.data) {
        setGoldPackages(response.data.golds);
        setMembershipPackages(response.data.memberships);
        setPayMethods(response.data.payMethods);
      }
    } catch (error) {
      console.error("Failed to fetch payment config:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCoinRecharge = async () => {
    // Check authentication first
    if (!isLoggedIn) {
      handleAuthenticationRequired();
      return;
    }

    if (!selectedGoldPackage || !userInfo || !selectedPayMethod) {
      console.error("Missing gold package, user info, or payment method");
      return;
    }

    try {
      setIsProcessingOrder(true);
      const api = createBaseApi();
      const payload = {
        userId: userInfo.id,
        goldId: selectedGoldPackage.id, // Gold package ID
        memberShip: "", // Empty for coin purchases
        payMethod: selectedPayMethod, // Selected payment method
      };

      console.log("Coin purchase payload:", payload);
      const response = await api.walletPay(payload);
      console.log("Coin purchase response:", response);

      if (response.ok && response.data) {
        // Response contains Stripe payment intent client secret
        const clientSecret = response.data;
        const amount = selectedGoldPackage.price; // Use the price from gold package

        onPaymentInitiated(clientSecret, amount);
      } else {
        console.error("Coin purchase failed:", response.msg);
      }
    } catch (error) {
      console.error("Error during coin purchase:", error);
    } finally {
      setIsProcessingOrder(false);
    }
  };

  // Authentication helper functions
  const handleAuthenticationRequired = () => {
    onAuthenticationRequired();
  };

  const handleVipPurchase = async () => {
    // Check authentication first
    if (!isLoggedIn) {
      handleAuthenticationRequired();
      return;
    }

    if (!selectedMembershipPackage || !userInfo || !selectedPayMethod) {
      console.error("Missing membership package, user info, or payment method");
      return;
    }

    try {
      setIsProcessingOrder(true);
      const api = createBaseApi();
      const payload = {
        userId: userInfo.id,
        goldId: "", // Empty for membership purchases
        memberShip: selectedMembershipPackage.id, // Membership package ID
        payMethod: selectedPayMethod, // Selected payment method
      };

      console.log("VIP purchase payload:", payload);
      const response = await api.walletPay(payload);
      console.log("VIP purchase response:", response);

      if (response.ok && response.data) {
        // Response contains Stripe payment intent client secret
        const clientSecret = response.data;
        const amount = selectedMembershipPackage.price; // Use the price from membership package
        onPaymentInitiated(clientSecret, amount);
      } else {
        console.error("VIP purchase failed:", response.msg);
      }
    } catch (error) {
      console.error("Error during VIP purchase:", error);
    } finally {
      setIsProcessingOrder(false);
    }
  };

  const handlePayNow = () => {
    if (selectedMembershipPackage) {
      // VIP purchase
      handleVipPurchase();
    } else if (selectedGoldPackage) {
      // Coin recharge
      handleCoinRecharge();
    }
  };

  // Mutual exclusion handlers
  const handleGoldSelection = (goldPackage: GoldPackage) => {
    setSelectedGoldPackage(goldPackage);
    setSelectedMembershipPackage(null); // Clear membership selection
    setSelectedPayMethod(null); // Clear payment method to force reselection
  };

  const handleMembershipSelection = (membershipPackage: MembershipPackage) => {
    setSelectedMembershipPackage(membershipPackage);
    setSelectedGoldPackage(null); // Clear gold selection
    setSelectedPayMethod(null); // Clear payment method to force reselection
  };

  return (
    <div className="text-white p-6">
      {/* Header */}
      {(title || subtitle) && (
        <div className="mb-6 text-center">
          {title && <h1 className="text-2xl font-bold mb-2">{title}</h1>}
          {subtitle && <p className="text-gray-400">{subtitle}</p>}
          {userInfo && (
            <p className="text-sm text-gray-400 mt-2">
              {t("topup.coinBalance")}: {userInfo.gold} {t("topup.coins")}
            </p>
          )}
        </div>
      )}

      <div className="max-w-4xl mx-auto">
        {/* Coins Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">{t("topup.coins")}</h2>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {goldPackages.map((goldPackage, index) => (
                <div
                  key={goldPackage.id}
                  onClick={() => handleGoldSelection(goldPackage)}
                  className={`relative bg-gradient-to-b from-orange-50 to-orange-100 text-black rounded-xl cursor-pointer transition-all overflow-hidden flex flex-col ${
                    selectedGoldPackage?.id === goldPackage.id
                      ? "ring-2 ring-brand-red ring-offset-2 ring-offset-black"
                      : "border-transparent hover:border-orange-200"
                  }`}
                >
                  {index === 3 && (
                    <div className="absolute right-0 bg-red-500 text-white text-xs px-2 py-1 rounded-tr-lg rounded-bl-lg font-medium z-10">
                      <Flame className="inline-block h-4 w-4" />{" "}
                      {t("topup.hot")}
                    </div>
                  )}
                  <div className="text-center space-y-2 p-4 flex-1">
                    <div className="text-2xl font-bold text-gray-800">
                      {goldPackage.value}
                      {goldPackage.reward > 0 && (
                        <span className="text-sm text-green-600 ml-1">
                          +{goldPackage.reward}
                        </span>
                      )}
                    </div>
                    <div className="text-sm font-medium text-gray-600">
                      {t("topup.coins")}
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white py-2.5 px-4 rounded-b-lg font-semibold text-sm shadow-md">
                    {formatCurrency(goldPackage.price)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Membership Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">
            {t("topup.membership")}
          </h2>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {membershipPackages.map((pkg, index) => (
                <div
                  key={pkg.id}
                  onClick={() => handleMembershipSelection(pkg)}
                  className={`relative bg-gradient-to-b from-yellow-100 to-yellow-200 text-black rounded-lg p-6 cursor-pointer transition-all ${
                    selectedMembershipPackage?.id === pkg.id
                      ? "ring-2 ring-brand-red ring-offset-2 ring-offset-black"
                      : ""
                  }`}
                >
                  {index === 1 && (
                    <div className="absolute right-0 top-0 bg-red-500 text-white text-xs px-2 py-1 rounded-tr-lg rounded-bl-lg font-medium z-10">
                      <Flame className="inline-block h-4 w-4" />{" "}
                      {t("topup.hot")}
                    </div>
                  )}
                  <div className="mb-3">
                    <div className="text-sm text-gray-600 mb-1">
                      {t("topup.membershipUnlimited")}
                    </div>
                    <div className="text-2xl font-bold mb-2">
                      {formatCurrency(pkg.price)}{" "}
                      <span className="text-sm font-normal">
                        / {pkg.circleType}
                      </span>
                    </div>
                    <div className="text-lg font-semibold text-gray-800 mb-2">
                      {pkg.title}
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="text-xs">{pkg.introduce}</div>
                    </div>
                    <div className="text-sm text-gray-600 mt-2">
                      {t("topup.autoRenew")}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Payment Method Section - Show when a package is selected */}
        {(selectedGoldPackage || selectedMembershipPackage) && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">
              {t("topup.paymentMethod")}
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-2 gap-4 max-w-2xl">
              {payMethods.map((method) => (
                <div
                  key={method}
                  onClick={() => setSelectedPayMethod(method)}
                  className={`rounded-lg p-4 cursor-pointer transition-all flex items-center justify-center ${
                    selectedPayMethod === method
                      ? "ring-2 ring-brand-red ring-offset-2 ring-offset-black bg-blue-100"
                      : "bg-gray-200 hover:bg-gray-300"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {method === "paypal" && (
                      <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                        <span className="text-white font-bold text-xs">
                          PayPal
                        </span>
                      </div>
                    )}
                    {method === "credit_card" && (
                      <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                        <CreditCard className="w-5 h-5 text-white" />
                      </div>
                    )}
                    <div className="text-center">
                      <div className="text-gray-700 font-medium text-sm">
                        {method === "paypal" && "PayPal"}
                        {method === "credit_card" && t("topup.creditCard")}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Pay Now Button */}
        <div className="mt-8 flex justify-end">
          <button
            onClick={handlePayNow}
            disabled={
              isProcessingOrder ||
              (!selectedMembershipPackage && !selectedGoldPackage) ||
              !selectedPayMethod
            }
            className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200"
          >
            {isProcessingOrder ? t("topup.processing") : t("topup.payNow")}
          </button>
        </div>
      </div>
    </div>
  );
};
