import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { LockedVideoOverlayProps } from "~/types/videos";
import TopUpDialog from "../TopUpDialog";
import ConfirmPurchaseDialog from "../ConfirmPurchaseDialog";
import { useUser } from "~/context/auth-context";
import { createBaseApi } from "~/utils/base-service";
import { PRICING } from "~/constants/currency";
import { useParams } from "@remix-run/react";

/**
 * Component that displays over a locked video, prompting user to pay/top-up
 */
const LockedVideoOverlay: React.FC<LockedVideoOverlayProps> = ({
  posterUrl,
  seriesTitle,
  onTopUpClick,
  onNavigateClick,
}) => {
  const { t } = useTranslation();
  const { isLoggedIn, userInfo } = useUser();
  const videoId = useParams().videoId;
  const [showTopUpDialog, setShowTopUpDialog] = useState(false);
  const [showConfirmPurchaseDialog, setShowConfirmPurchaseDialog] =
    useState(false);

  const unlockCost = PRICING.UNLOCK_EPISODE_COST;

  const handleUnlockClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling

    // Call the original onTopUpClick if provided (for backward compatibility)
    if (onTopUpClick) {
      onTopUpClick();
    }

    // Check if user is logged in
    if (!isLoggedIn || !userInfo) {
      setShowTopUpDialog(true);
      return;
    }

    // Check if user has enough coins
    if (userInfo.gold < unlockCost) {
      setShowTopUpDialog(true);
      return;
    }

    // User has enough coins, show confirm purchase dialog
    setShowConfirmPurchaseDialog(true);
  };

  const handleConfirmPurchase = async () => {
    if (!userInfo) {
      throw new Error("User not logged in");
    }

    if (!videoId) {
      throw new Error("Video ID is required to unlock content");
    }

    const api = createBaseApi();
    const response = await api.buyPost({
      id: videoId,
    });

    if (!response.ok) {
      throw new Error(response.msg || "Failed to unlock content");
    }

    // Success - the dialog will show success state
    // You might want to refresh user info or trigger a callback here
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    // If clicking outside the unlock button and navigation is available, navigate
    if (onNavigateClick && e.target === e.currentTarget) {
      onNavigateClick();
    }
  };

  return (
    <div className="absolute inset-0 z-40 pointer-events-none">
      {/* Lock indicator overlay - only covers part of the video */}
      <div className="absolute top-4 right-4 bg-black/80 rounded-lg p-3 pointer-events-auto">
        <div className="flex items-center gap-2 text-white">
          <span className="w-3 h-3 bg-red-600 rounded-full"></span>
          <span className="text-sm font-medium">{t("video.locked")}</span>
        </div>
      </div>

      {/* Center unlock prompt - appears on hover or click */}
      <div
        className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center p-6 text-center opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-auto cursor-pointer"
        onClick={handleOverlayClick}
      >
        <div className="flex flex-col items-center justify-center">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-6">
            {onNavigateClick
              ? t("video.clickToViewFullVideo")
              : t("video.clickToUnlockFullVideo")}
          </h1>

          <button
            onClick={handleUnlockClick}
            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-8 rounded-lg text-base shadow-lg transition-colors duration-150 flex items-center gap-2"
          >
            <span className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
              <span className="w-2 h-2 bg-red-600 rounded-full"></span>
            </span>
            {t("video.unlockNow")}
          </button>
        </div>
      </div>

      {/* Bottom info section - only visible on hover */}
      <div className="absolute bottom-4 left-4 flex items-center gap-3 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-auto">
        <div className="w-12 h-16 rounded-md overflow-hidden shadow-lg">
          <img
            src={posterUrl || "https://placehold.co/64x96/333/555?text=POSTER"}
            alt={seriesTitle || t("video.defaultTitle")}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="text-left">
          <h2 className="text-sm font-bold text-white">
            {seriesTitle || t("video.lockedContent")}
          </h2>
          <p className="text-xs text-gray-400">{t("video.premiumContent")}</p>
        </div>
      </div>

      {/* TopUp Dialog */}
      <TopUpDialog
        open={showTopUpDialog}
        onOpenChange={setShowTopUpDialog}
        title={t("video.unlockSubsequentEpisodes")}
        subtitle={t("video.priceToUnlock", { cost: unlockCost })}
      />

      {/* Confirm Purchase Dialog */}
      <ConfirmPurchaseDialog
        open={showConfirmPurchaseDialog}
        onOpenChange={setShowConfirmPurchaseDialog}
        currentBalance={userInfo?.gold || 0}
        onConfirmPurchase={handleConfirmPurchase}
      />
    </div>
  );
};

export default LockedVideoOverlay;
