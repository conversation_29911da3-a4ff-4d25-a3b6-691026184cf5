import React, { useState, useEffect } from "react";
import {
  useSearchParams,
  useLoaderData,
  ClientLoaderFunctionArgs,
} from "@remix-run/react";
import { Search, X, Loader2 } from "lucide-react";
import AppHeader from "~/components/AppHeader";
import Footer from "~/components/Footer";
import MobileFooter from "~/components/MobileFooter";
import MovieCard from "~/components/ui/MovieCard";
import { Input } from "~/components/ui/Input";
import { useDebounceSearch } from "~/hooks/useDebounceSearch";
import { useTranslation } from "react-i18next";

// Simple loader that just returns initial state
export async function clientLoader({ request }: ClientLoaderFunctionArgs) {
  const url = new URL(request.url);
  const initialQuery = url.searchParams.get("q") || "";

  return {
    initialQuery,
  };
}

export default function SearchPage() {
  const { t } = useTranslation();
  const data = useLoaderData<typeof clientLoader>();
  const [, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(data.initialQuery || "");
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Use debounced search hook
  const {
    searchResult,
    search,
    clearSearch: clearSearchResults,
  } = useDebounceSearch({
    delay: 500,
    minLength: 1,
  });

  // Load recent searches from localStorage on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("recentSearches");
      if (saved) {
        try {
          setRecentSearches(JSON.parse(saved));
        } catch (e) {
          console.error("Failed to parse recent searches:", e);
        }
      }
    }
  }, []);

  // Initialize search with URL query parameter
  useEffect(() => {
    if (data.initialQuery) {
      search(data.initialQuery);
    }
  }, [data.initialQuery, search]);

  // Save search to recent searches
  const saveToRecentSearches = (query: string) => {
    if (!query.trim()) return;

    const updated = [query, ...recentSearches.filter((s) => s !== query)].slice(
      0,
      5
    );
    setRecentSearches(updated);

    if (typeof window !== "undefined") {
      localStorage.setItem("recentSearches", JSON.stringify(updated));
    }
  };

  const handleSearch = (query: string) => {
    if (query.trim()) {
      setSearchParams({ q: query });
      saveToRecentSearches(query);
      search(query); // Trigger debounced search
    } else {
      setSearchParams({});
      clearSearchResults(); // Clear search results
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(searchQuery);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSearchParams({});
    clearSearchResults();
  };

  // Handle input change with debounced search
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Update URL and trigger search as user types
    if (value.trim()) {
      setSearchParams({ q: value });
      search(value); // This will be debounced
    } else {
      setSearchParams({});
      clearSearchResults();
    }
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    if (typeof window !== "undefined") {
      localStorage.removeItem("recentSearches");
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <AppHeader />
      <main className="flex-grow bg-main-bg">
        <div className="max-w-screen-2xl mx-auto px-4 py-6">
          {/* Search Header */}
          <div className="mb-8">
            <h1 className="text-white text-2xl font-bold mb-4">
              {t("search.title")}
            </h1>

            {/* Search Form */}
            <form onSubmit={handleSubmit}>
              <Input
                type="text"
                value={searchQuery}
                onChange={handleInputChange}
                placeholder={t("search.placeholder")}
                leftIcon={
                  searchResult.isLoading ? (
                    <Loader2 size={20} className="text-gray-400 animate-spin" />
                  ) : (
                    <Search size={20} className="text-gray-400" />
                  )
                }
                rightIcon={
                  searchQuery ? (
                    <X size={20} className="text-gray-400 hover:text-white" />
                  ) : undefined
                }
                onRightIconClick={searchQuery ? clearSearch : undefined}
                className="bg-neutral-800 text-white border-neutral-700 focus:border-primary"
              />
            </form>
          </div>

          {/* Content Area */}
          {!searchResult.query ? (
            /* Show recent searches and popular content when no search */
            <div className="space-y-8">
              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-white text-lg font-semibold">
                      {t("search.recentSearches")}
                    </h2>
                    <button
                      onClick={clearRecentSearches}
                      className="text-gray-400 hover:text-white text-sm"
                    >
                      {t("search.clearAll")}
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          setSearchQuery(search);
                          handleSearch(search);
                        }}
                        className="bg-neutral-800 text-white px-3 py-2 rounded-full text-sm hover:bg-neutral-700 transition-colors"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Show search results */
            <div>
              {searchResult.error ? (
                <div className="text-center py-12">
                  <p className="text-gray-400">{searchResult.error}</p>
                </div>
              ) : searchResult.results.length > 0 ? (
                <div>
                  <div className="mb-6">
                    <p className="text-gray-400">
                      {t("search.resultsFor")} "{searchResult.query}" (
                      {searchResult.total} {t("search.results")})
                    </p>
                  </div>
                  <div className="grid grid-cols-3 gap-x-3 gap-y-5 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6">
                    {searchResult.results.map((item) => (
                      <MovieCard key={item.id} item={item} />
                    ))}
                  </div>
                </div>
              ) : searchResult.isLoading ? (
                <div className="text-center py-12">
                  <Loader2
                    className="mx-auto animate-spin text-gray-400 mb-4"
                    size={32}
                  />
                  <p className="text-gray-400">{t("search.searching")}</p>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-400 mb-4">
                    {t("search.noResults")} "{searchResult.query}"
                  </p>
                  <p className="text-gray-500 text-sm">
                    {t("search.tryDifferent")}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
      <Footer />
      <MobileFooter />
    </div>
  );
}
