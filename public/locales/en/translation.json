{"app": {"tagline": "Short videos for you"}, "nav": {"browse": "Browse", "trending": "Trending", "following": "Following", "profile": "Profile", "goBack": "Go back"}, "language": {"title": "Language", "english": "English", "chinese": "Chinese", "russian": "Russian", "vietnamese": "Vietnamese", "hindi": "Hindi", "spanish": "Spanish", "armenian": "Armenian"}, "videoFeed": {"popular": "Popular", "watchAgain": "Watch Again", "newReleases": "New Releases", "tvShows": "TV Shows", "genres": "Genre"}, "genres": {"title": "Genre", "action": "Action", "comedy": "Comedy", "drama": "Drama", "horror": "Horror", "romance": "Romance", "scifi": "Sci-Fi", "thriller": "Thriller", "documentary": "Documentary", "all": "All Genres"}, "featured": {"playNow": "Play Now", "addWatchlist": "Add Watchlist"}, "home": {"lastWatched": "Last Watched", "popularOfTheWeek": "Popular of the Week"}, "trending": {"trending": "Trending", "thisWeek": "Trending This Week", "thisMonth": "Trending This Month"}, "video": {"playNow": "Play Now", "addToList": "Add to List", "unknownTitle": "Unknown Video", "loadingVideo": "Loading video...", "defaultTitle": "Video Title", "noDescription": "No description available.", "notFound": "Video not found.", "addedToWatchlist": "Added to Watchlist", "removedFromWatchlist": "Removed from watchlist", "videoLiked": "Video Liked", "videoSaved": "Video Saved", "shareTriggered": "Share action triggered", "showLess": "Show less", "readMore": "Read more", "less": "less", "more": "more", "season": "Season", "recommendationsForYou": "Recommendations for you", "enableSubtitles": "Enable subtitles", "disableSubtitles": "Disable subtitles", "unlock": "Unlock", "unlockThisVideo": "Unlock this video", "unlockWithCoins": "Unlock with coins", "unlockWithVip": "Unlock with VIP", "getVip": "Get VIP", "notEnoughCoins": "Not enough coins", "purchaseSuccessful": "Purchase successful!", "purchaseFailed": "Purchase failed", "videoUnlocked": "Video unlocked successfully!", "episodes": "Episodes", "allEpisodes": "All Episodes", "currentEpisode": "Current Episode", "nextEpisode": "Next Episode", "previousEpisode": "Previous Episode", "autoPlay": "Auto Play", "quality": "Quality", "speed": "Speed", "volume": "Volume", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "mute": "Mute", "unmute": "Unmute", "replay": "Replay", "forward": "Forward", "backward": "Backward", "settings": "Settings", "close": "Close", "locked": "Locked", "clickToViewFullVideo": "Click to View Full Video", "clickToUnlockFullVideo": "Click to Unlock Full Video", "unlockNow": "Unlock Now", "lockedContent": "Locked Content", "premiumContent": "Premium Content", "unlockSubsequentEpisodes": "Unlock subsequent episodes", "priceToUnlock": "Price : {cost} Coins to unlock this subsequent episodes"}, "series": {"unknownTitle": "Unknown Series"}, "episode": {"loadingEpisode": "Loading episode...", "noEpisodesAvailable": "No episodes available", "allEpisodes": "All Episodes", "episodeList": "Episode List", "episode": "Episode", "ep": "EP"}, "common": {"collection": "Collection", "singleVideo": "Single Video", "episodesCap": "Episodes", "share": "Share"}, "toast": {"copiedToClipboard": "Copied to clipboard!", "topUpFeatureComingSoon": "Top-up feature coming soon!"}, "topup": {"title": "Top Up", "balance": "Balance", "coins": "Coins", "days": "days", "hot": "Hot", "noPackages": "No VIP packages available", "viewAll": "View All Packages", "membership": "Membership", "paymentMethod": "Payment Method", "payNow": "Pay Now", "rewardCoins": "<PERSON>ward <PERSON>ins", "unlimitedAccess": "Unlimited access to all series", "autoRenew": "Auto renew and Cancel anytime", "creditCard": "Credit Card", "selectCoinAmount": "Please select a coin amount first", "loginRequired": "Please log in to access top-up options", "membershipUnlimited": "Membership | Unlimited access to all series", "loadingText": "Loading...", "processing": "Processing...", "coinBalance": "Coin Balance"}, "error": {"defaultMessage": "Sorry, we couldn't find the page you're looking for.", "internalServerError": "Internal Server Error", "somethingWentWrong": "Something went wrong. Please try again later.", "backToHome": "Back to Home"}, "auth": {"login": {"title": "Hi, Welcome Back!", "signIn": "Sign In", "usernameOrEmail": "Username or Email", "usernameOrEmailPlaceholder": "Enter your username or email", "password": "Password", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password", "continueWithEmail": "Continue with <PERSON>ail", "signingIn": "Signing In...", "orContinueWith": "Or continue with", "continueWithFacebook": "Continue with Facebook", "continueWithGoogle": "Continue with Google", "continueWithApple": "Continue with Apple", "dontHaveAccount": "Don't have an account?", "signUp": "Sign Up", "errors": {"usernameRequired": "Username or email is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "enterUsername": "Please enter your username or email address.", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "unexpectedError": "An unexpected error occurred during login."}}, "register": {"signUp": "Sign Up", "verification": "Verification", "completeAccount": "Complete your account", "enterDetails": "Enter your details to create an account", "nickname": "Nickname", "nicknamePlaceholder": "Enter your nickname", "email": "Email", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "processing": "Processing...", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign In", "enterVerificationCode": "Enter Verification Code", "verificationCodeSent": "We have sent a verification code to your email", "completeRegistration": "Complete Registration", "verifying": "Verifying...", "didntReceiveCode": "Didn't receive the verification code?", "resendCode": "Resend Code", "errors": {"nicknameRequired": "Nickname is required", "emailRequired": "Email is required", "invalidEmail": "Invalid email address", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "validOtp": "Please enter a valid 4-digit OTP.", "sendCodeFailed": "Failed to send verification code. Please try again.", "registrationFailed": "Registration failed. Please try again.", "resendCodeFailed": "Failed to resend verification code. Please try again."}, "success": {"verificationCodeSent": "Verification code has been sent to your email.", "newVerificationCodeSent": "A new verification code has been sent to your email.", "registrationSuccessful": "Registration successful! Redirecting to login..."}}, "forgotPassword": {"title": "Forgot Password", "subtitle": "You forgot your password? don't worry, please enter your recovery email address", "resetTitle": "Reset Password?", "resetSubtitle": "Please enter your new password to update the password", "email": "Email", "emailPlaceholder": "Enter your email address", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Enter verification code", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Enter your password", "submit": "Submit", "submitting": "Submitting...", "errors": {"emailRequired": "Email is required", "invalidEmail": "Invalid email address", "verificationCodeRequired": "Verification code is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "sendCodeFailed": "Failed to send verification code. Please try again.", "resetFailed": "Failed to reset password. Please try again."}, "success": {"verificationCodeSent": "Verification code has been sent to your email.", "passwordResetSuccessful": "Password reset successful! Redirecting to login..."}}, "logoutConfirmTitle": "Are you sure you want to log out?", "confirmLogout": "Confirm Log Out"}, "profile": {"title": "Profile", "myAccount": "My Account", "myVideos": "My Videos", "settings": "Settings", "logout": "Logout"}, "settings": {"title": "User Settings", "account": {"title": "My Account", "personalInfo": {"title": "Personal Information", "subtitle": "Provide your information so that your account can operate correctly.", "fullName": "Full Name", "email": "Email", "phone": "Phone"}, "password": {"title": "Password", "subtitle": "Set a password that is unique", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "currentPasswordPlaceholder": "Enter your current password", "newPasswordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Confirm your new password"}, "saveChanges": "Save Changes", "saving": "Saving...", "errors": {"fullNameRequired": "Full name is required", "emailRequired": "Email is required", "invalidEmail": "Invalid email address", "phoneRequired": "Phone is required", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "passwordMinLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "updateFailed": "Failed to update account. Please try again."}, "success": {"accountUpdated": "Account updated successfully!", "personalInfoUpdated": "Personal information updated successfully!", "passwordUpdated": "Password changed successfully!"}}}, "loading": "Loading...", "loadingContent": "Loading content...", "loadingVideo": "Loading video...", "loadingApplication": "Loading application...", "open": "Open", "search": {"title": "Search", "placeholder": "Search by title, genre, actor...", "noResults": "No results found for", "searchResults": "Search Results", "recentSearches": "Recent Search", "clearAll": "Clear All", "searching": "Searching...", "tryDifferentKeywords": "Try different keywords or check your spelling", "tryDifferent": "Try searching with different keywords", "resultsFor": "Results for", "results": "results"}, "navigation": {"home": "Home", "genre": "Genre", "popular": "Popular", "myWatchlist": "My Watchlist", "browse": "Browse", "following": "Following"}, "wallet": {"title": "My Wallet", "balance": "Balance", "transactionHistory": "Transaction History", "noTransactions": "No transactions yet", "date": "Date", "amount": "Amount", "type": "Type", "status": "Status"}}